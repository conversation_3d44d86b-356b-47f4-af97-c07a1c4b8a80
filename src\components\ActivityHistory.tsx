
import { useState, useMemo } from "react";
import { ActivityTable } from "./ActivityTable";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format } from "date-fns";
import { vi } from "date-fns/locale";
import { Filter, Download, CalendarIcon } from "lucide-react";
import { cn } from "@/lib/utils";

interface Activity {
  id: number;
  type: "walkie-talkie" | "access-card";
  deviceName: string;
  action: "borrowed" | "returned";
  borrower: string;
  timestamp: Date;
  duration?: string;
}

export const ActivityHistory = () => {
  const [selectedDate, setSelectedDate] = useState<Date>();
  const [filterType, setFilterType] = useState<"all" | "walkie-talkie" | "access-card">("all");

  // Mock data - trong thực tế sẽ lấy từ database
  const [activities] = useState<Activity[]>([
    {
      id: 1,
      type: "walkie-talkie",
      deviceName: "Bộ đàm 002",
      action: "borrowed",
      borrower: "Nguyễn <PERSON>n A",
      timestamp: new Date(2024, 5, 3, 9, 30),
    },
    {
      id: 2,
      type: "access-card",
      deviceName: "Thẻ A-002",
      action: "borrowed",
      borrower: "Trần Thị B",
      timestamp: new Date(2024, 5, 3, 10, 15),
    },
    {
      id: 3,
      type: "walkie-talkie",
      deviceName: "Bộ đàm 001",
      action: "returned",
      borrower: "Lê Văn C",
      timestamp: new Date(2024, 5, 2, 16, 45),
      duration: "6 giờ 15 phút",
    },
    {
      id: 4,
      type: "access-card",
      deviceName: "Thẻ A-001",
      action: "returned",
      borrower: "Phạm Thị D",
      timestamp: new Date(2024, 5, 2, 14, 20),
      duration: "4 giờ 30 phút",
    },
    {
      id: 5,
      type: "walkie-talkie",
      deviceName: "Bộ đàm 003",
      action: "borrowed",
      borrower: "Hoàng Văn E",
      timestamp: new Date(2024, 5, 1, 8, 0),
    },
  ]);

  // Filter data based on date and type (search is handled by ActivityTable)
  const filteredActivities = useMemo(() => {
    return activities.filter(activity => {
      const matchesType = filterType === "all" || activity.type === filterType;
      const matchesDate = !selectedDate ||
        activity.timestamp.toDateString() === selectedDate.toDateString();

      return matchesType && matchesDate;
    });
  }, [activities, filterType, selectedDate]);

  const exportToCSV = () => {
    const headers = ["Ngày", "Thiết bị", "Loại", "Hành động", "Người mượn", "Thời gian sử dụng"];
    const csvContent = [
      headers.join(","),
      ...filteredActivities.map(activity => [
        activity.timestamp.toLocaleDateString('vi-VN'),
        activity.deviceName,
        activity.type === "walkie-talkie" ? "Bộ đàm" : "Thẻ ra vào",
        activity.action === "borrowed" ? "Mượn" : "Trả",
        activity.borrower,
        activity.duration || "Đang mượn"
      ].join(","))
    ].join("\n");

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute("download", `lich-su-hoat-dong-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="space-y-6">
      {/* Filters */}
      <div className="flex flex-wrap items-center gap-4 p-4 bg-white rounded-lg shadow-sm">
        <div className="flex items-center space-x-2">
          <Filter className="h-4 w-4 text-gray-500" />
          <span className="text-sm font-medium">Bộ lọc:</span>
        </div>

        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                "justify-start text-left font-normal",
                !selectedDate && "text-muted-foreground"
              )}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {selectedDate ? format(selectedDate, "PPP", { locale: vi }) : "Chọn ngày"}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              mode="single"
              selected={selectedDate}
              onSelect={setSelectedDate}
              initialFocus
              className="p-3 pointer-events-auto"
            />
          </PopoverContent>
        </Popover>

        <div className="flex space-x-2">
          <Button
            variant={filterType === "all" ? "default" : "outline"}
            size="sm"
            onClick={() => setFilterType("all")}
          >
            Tất cả
          </Button>
          <Button
            variant={filterType === "walkie-talkie" ? "default" : "outline"}
            size="sm"
            onClick={() => setFilterType("walkie-talkie")}
          >
            Bộ đàm
          </Button>
          <Button
            variant={filterType === "access-card" ? "default" : "outline"}
            size="sm"
            onClick={() => setFilterType("access-card")}
          >
            Thẻ ra vào
          </Button>
        </div>

        {selectedDate && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setSelectedDate(undefined)}
          >
            Xóa bộ lọc ngày
          </Button>
        )}

        <Button onClick={exportToCSV} variant="outline" size="sm" className="ml-auto">
          <Download className="h-4 w-4 mr-2" />
          Xuất Excel
        </Button>
      </div>

      {/* Activity Table */}
      <ActivityTable data={filteredActivities} />
    </div>
  );
};
