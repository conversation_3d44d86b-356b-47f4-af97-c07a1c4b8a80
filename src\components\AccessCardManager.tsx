
import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { CreditCard, Plus, UserCheck, UserX, Search } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useForm } from "@tanstack/react-form";
import { addDeviceSchema, borrowDeviceSchema, type AddDeviceFormData, type BorrowDeviceFormData } from "@/lib/schemas";

interface AccessCard {
  id: number;
  name: string;
  status: "available" | "borrowed";
  borrower: string | null;
  borrowedAt: Date | null;
}

interface AccessCardManagerProps {
  initialData: AccessCard[];
}

export const AccessCardManager = ({ initialData }: AccessCardManagerProps) => {
  const [accessCards, setAccessCards] = useState<AccessCard[]>(initialData);
  const [searchTerm, setSearchTerm] = useState("");
  const [newCardName, setNewCardName] = useState("");
  const [borrowerName, setBorrowerName] = useState("");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isBorrowDialogOpen, setIsBorrowDialogOpen] = useState(false);
  const [selectedCard, setSelectedCard] = useState<AccessCard | null>(null);
  const { toast } = useToast();

  const filteredCards = accessCards.filter(card =>
    card.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (card.borrower && card.borrower.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const addCard = () => {
    if (!newCardName.trim()) return;
    
    const newCard: AccessCard = {
      id: Date.now(),
      name: newCardName,
      status: "available",
      borrower: null,
      borrowedAt: null
    };
    
    setAccessCards([...accessCards, newCard]);
    setNewCardName("");
    setIsAddDialogOpen(false);
    
    toast({
      title: "Thành công",
      description: `Đã thêm thẻ ra vào ${newCardName}`,
    });
  };

  const borrowCard = () => {
    if (!borrowerName.trim() || !selectedCard) return;
    
    const updatedCards = accessCards.map(card =>
      card.id === selectedCard.id
        ? {
            ...card,
            status: "borrowed" as const,
            borrower: borrowerName,
            borrowedAt: new Date()
          }
        : card
    );
    
    setAccessCards(updatedCards);
    setBorrowerName("");
    setIsBorrowDialogOpen(false);
    setSelectedCard(null);
    
    toast({
      title: "Cho mượn thành công",
      description: `${selectedCard.name} đã được cho ${borrowerName} mượn`,
    });
  };

  const returnCard = (card: AccessCard) => {
    const updatedCards = accessCards.map(c =>
      c.id === card.id
        ? {
            ...c,
            status: "available" as const,
            borrower: null,
            borrowedAt: null
          }
        : c
    );
    
    setAccessCards(updatedCards);
    
    toast({
      title: "Trả thiết bị thành công",
      description: `${card.name} đã được trả về`,
    });
  };

  const openBorrowDialog = (card: AccessCard) => {
    setSelectedCard(card);
    setIsBorrowDialogOpen(true);
  };

  return (
    <div className="space-y-6">
      <Card className="bg-white shadow-sm">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <CreditCard className="h-5 w-5 text-green-600" />
              <span>Quản lý Thẻ ra vào</span>
            </div>
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button className="bg-green-600 hover:bg-green-700">
                  <Plus className="h-4 w-4 mr-2" />
                  Thêm thẻ ra vào
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Thêm thẻ ra vào mới</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="card-name">Tên thẻ ra vào</Label>
                    <Input
                      id="card-name"
                      value={newCardName}
                      onChange={(e) => setNewCardName(e.target.value)}
                      placeholder="VD: Thẻ A-004"
                    />
                  </div>
                  <Button onClick={addCard} className="w-full">Thêm</Button>
                </div>
              </DialogContent>
            </Dialog>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="mb-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Tìm kiếm thẻ ra vào hoặc người mượn..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredCards.map((card) => (
              <Card key={card.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-semibold text-gray-900">{card.name}</h3>
                    <Badge 
                      variant={card.status === "available" ? "default" : "destructive"}
                      className={card.status === "available" ? "bg-green-100 text-green-800" : "bg-orange-100 text-orange-800"}
                    >
                      {card.status === "available" ? "Có sẵn" : "Đang mượn"}
                    </Badge>
                  </div>
                  
                  {card.status === "borrowed" && (
                    <div className="mb-3 text-sm text-gray-600">
                      <p><strong>Người mượn:</strong> {card.borrower}</p>
                      <p><strong>Thời gian:</strong> {card.borrowedAt?.toLocaleString('vi-VN')}</p>
                    </div>
                  )}
                  
                  <div className="flex space-x-2">
                    {card.status === "available" ? (
                      <Button 
                        onClick={() => openBorrowDialog(card)}
                        className="flex-1 bg-green-600 hover:bg-green-700"
                        size="sm"
                      >
                        <UserCheck className="h-4 w-4 mr-1" />
                        Cho mượn
                      </Button>
                    ) : (
                      <Button 
                        onClick={() => returnCard(card)}
                        variant="outline"
                        className="flex-1"
                        size="sm"
                      >
                        <UserX className="h-4 w-4 mr-1" />
                        Trả về
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Borrow Dialog */}
      <Dialog open={isBorrowDialogOpen} onOpenChange={setIsBorrowDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Cho mượn {selectedCard?.name}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="borrower-name">Tên người mượn</Label>
              <Input
                id="borrower-name"
                value={borrowerName}
                onChange={(e) => setBorrowerName(e.target.value)}
                placeholder="Nhập tên người mượn"
              />
            </div>
            <Button onClick={borrowCard} className="w-full">Xác nhận cho mượn</Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};
