import { useState, useMemo } from "react";
import {
  useReactTable,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  getPaginationRowModel,
  createColumnHelper,
  flexRender,
  type SortingState,
  type ColumnFiltersState,
  type PaginationState,
} from "@tanstack/react-table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Clock, 
  Search, 
  Radio, 
  CreditCard, 
  ChevronUp, 
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight
} from "lucide-react";
import { format } from "date-fns";
import { vi } from "date-fns/locale";

interface Activity {
  id: number;
  type: "walkie-talkie" | "access-card";
  deviceName: string;
  action: "borrowed" | "returned";
  borrower: string;
  timestamp: Date;
  duration?: string;
}

interface ActivityTableProps {
  data: Activity[];
}

const columnHelper = createColumnHelper<Activity>();

export const ActivityTable = ({ data }: ActivityTableProps) => {
  const [sorting, setSorting] = useState<SortingState>([
    { id: 'timestamp', desc: true } // Mặc định sắp xếp theo thời gian mới nhất
  ]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState("");
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  const columns = useMemo(
    () => [
      columnHelper.accessor("type", {
        header: "Loại",
        cell: (info) => (
          <div className="flex items-center space-x-2">
            {info.getValue() === "walkie-talkie" ? (
              <div className="bg-blue-100 p-1 rounded-full">
                <Radio className="h-3 w-3 text-blue-600" />
              </div>
            ) : (
              <div className="bg-green-100 p-1 rounded-full">
                <CreditCard className="h-3 w-3 text-green-600" />
              </div>
            )}
            <span className="text-sm">
              {info.getValue() === "walkie-talkie" ? "Bộ đàm" : "Thẻ ra vào"}
            </span>
          </div>
        ),
        filterFn: "includesString",
      }),
      columnHelper.accessor("deviceName", {
        header: "Thiết bị",
        cell: (info) => (
          <span className="font-medium">{info.getValue()}</span>
        ),
      }),
      columnHelper.accessor("action", {
        header: "Hành động",
        cell: (info) => (
          <Badge 
            variant={info.getValue() === "borrowed" ? "default" : "secondary"}
            className={info.getValue() === "borrowed" ? "bg-orange-100 text-orange-800" : "bg-green-100 text-green-800"}
          >
            {info.getValue() === "borrowed" ? "Cho mượn" : "Trả về"}
          </Badge>
        ),
        filterFn: "includesString",
      }),
      columnHelper.accessor("borrower", {
        header: "Người mượn",
        cell: (info) => (
          <span className="text-sm">{info.getValue()}</span>
        ),
      }),
      columnHelper.accessor("timestamp", {
        header: "Thời gian",
        cell: (info) => (
          <span className="text-sm text-gray-600">
            {format(info.getValue(), "dd/MM/yyyy HH:mm", { locale: vi })}
          </span>
        ),
        sortingFn: "datetime",
      }),
      columnHelper.accessor("duration", {
        header: "Thời lượng",
        cell: (info) => (
          <span className="text-sm text-gray-500">
            {info.getValue() || "-"}
          </span>
        ),
      }),
    ],
    []
  );

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnFilters,
      globalFilter,
      pagination,
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: setGlobalFilter,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  return (
    <Card className="bg-white shadow-sm">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Clock className="h-5 w-5 text-purple-600" />
          <span>Lịch sử hoạt động</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Global Search */}
        <div className="mb-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Tìm kiếm trong lịch sử..."
              value={globalFilter}
              onChange={(e) => setGlobalFilter(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Table */}
        <div className="rounded-md border">
          <table className="w-full">
            <thead>
              {table.getHeaderGroups().map((headerGroup) => (
                <tr key={headerGroup.id} className="border-b bg-gray-50">
                  {headerGroup.headers.map((header) => (
                    <th
                      key={header.id}
                      className="px-4 py-3 text-left text-sm font-medium text-gray-900"
                    >
                      {header.isPlaceholder ? null : (
                        <div
                          className={`flex items-center space-x-1 ${
                            header.column.getCanSort() ? "cursor-pointer select-none" : ""
                          }`}
                          onClick={header.column.getToggleSortingHandler()}
                        >
                          <span>
                            {flexRender(header.column.columnDef.header, header.getContext())}
                          </span>
                          {header.column.getCanSort() && (
                            <span className="ml-1">
                              {header.column.getIsSorted() === "asc" ? (
                                <ChevronUp className="h-4 w-4" />
                              ) : header.column.getIsSorted() === "desc" ? (
                                <ChevronDown className="h-4 w-4" />
                              ) : (
                                <div className="h-4 w-4" />
                              )}
                            </span>
                          )}
                        </div>
                      )}
                    </th>
                  ))}
                </tr>
              ))}
            </thead>
            <tbody>
              {table.getRowModel().rows.map((row) => (
                <tr key={row.id} className="border-b hover:bg-gray-50">
                  {row.getVisibleCells().map((cell) => (
                    <td key={cell.id} className="px-4 py-3">
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <div className="flex items-center justify-between mt-4">
          <div className="text-sm text-gray-700">
            Hiển thị {table.getState().pagination.pageIndex * table.getState().pagination.pageSize + 1} đến{" "}
            {Math.min(
              (table.getState().pagination.pageIndex + 1) * table.getState().pagination.pageSize,
              table.getFilteredRowModel().rows.length
            )}{" "}
            trong tổng số {table.getFilteredRowModel().rows.length} kết quả
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.setPageIndex(0)}
              disabled={!table.getCanPreviousPage()}
            >
              <ChevronsLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <span className="text-sm">
              Trang {table.getState().pagination.pageIndex + 1} / {table.getPageCount()}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.setPageIndex(table.getPageCount() - 1)}
              disabled={!table.getCanNextPage()}
            >
              <ChevronsRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
