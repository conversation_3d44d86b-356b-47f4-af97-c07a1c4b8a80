import { z } from 'zod'

// Schema cho thêm thiết bị mới
export const addDeviceSchema = z.object({
  name: z.string()
    .min(1, 'Tên thiết bị không được để trống')
    .min(3, 'Tên thiết bị phải có ít nhất 3 ký tự')
    .max(50, 'Tên thiết bị không được quá 50 ký tự')
})

// Schema cho cho mượn thiết bị
export const borrowDeviceSchema = z.object({
  borrowerName: z.string()
    .min(1, 'Tên người mượn không được để trống')
    .min(2, 'Tên người mượn phải có ít nhất 2 ký tự')
    .max(100, 'Tên người mượn không được quá 100 ký tự')
    .regex(/^[a-zA-ZÀ-ỹ\s]+$/, 'Tên người mượn chỉ được chứa chữ cái và khoảng trắng')
})

// Types từ schemas
export type AddDeviceFormData = z.infer<typeof addDeviceSchema>
export type BorrowDeviceFormData = z.infer<typeof borrowDeviceSchema>
